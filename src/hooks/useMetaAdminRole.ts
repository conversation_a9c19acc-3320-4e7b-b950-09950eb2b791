import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export type MetaAdminRole = 'SUPER_ADMIN' | 'SUPPORT_ADMIN' | 'BILLING_ADMIN' | 'READONLY';

export interface MetaAdminPermission {
  id: string;
  user_id: string;
  role: MetaAdminRole;
  granted_by: string;
  granted_at: string;
  expires_at?: string;
}

export function useMetaAdminRole() {
  const { user } = useAuth();
  const [roles, setRoles] = useState<MetaAdminRole[]>([]);
  const [permissions, setPermissions] = useState<MetaAdminPermission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      console.log('🔍 useMetaAdminRole: No user, clearing roles');
      setRoles([]);
      setPermissions([]);
      setIsLoading(false);
      return;
    }

    console.log('🔍 useMetaAdminRole: User found, loading roles for:', user.id);
    loadMetaAdminRoles();
  }, [user]);

  const loadMetaAdminRoles = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🔍 useMetaAdminRole: Loading roles for user:', user!.id);

      const { data, error } = await supabase
        .from('meta_admin_permissions')
        .select('*')
        .eq('user_id', user!.id)
        .or('expires_at.is.null,expires_at.gt.now()');

      console.log('🔍 useMetaAdminRole: Query result:', { data, error });

      if (error) throw error;

      const activePermissions = data || [];
      setPermissions(activePermissions);
      setRoles(activePermissions.map(p => p.role));

      console.log('🔍 useMetaAdminRole: Set roles:', activePermissions.map(p => p.role));
    } catch (err) {
      console.error('❌ Error loading meta admin roles:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const hasRole = (role: MetaAdminRole): boolean => {
    return roles.includes(role);
  };

  const hasAnyRole = (): boolean => {
    return roles.length > 0;
  };

  const canAccessSection = (section: string): boolean => {
    if (!hasAnyRole()) return false;

    // Super admins can access everything
    if (hasRole('SUPER_ADMIN')) return true;

    // Define access rules for different sections
    const accessRules: Record<string, MetaAdminRole[]> = {
      dashboard: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'BILLING_ADMIN', 'READONLY'],
      clubs: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      domains: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      users: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      billing: ['SUPER_ADMIN', 'BILLING_ADMIN'],
      content: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      integrations: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      monitoring: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      support: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      settings: ['SUPER_ADMIN'],
    };

    const allowedRoles = accessRules[section] || [];
    return roles.some(role => allowedRoles.includes(role));
  };

  const canModify = (): boolean => {
    // Only SUPER_ADMIN and specific roles can modify data
    return hasRole('SUPER_ADMIN') || hasRole('SUPPORT_ADMIN') || hasRole('BILLING_ADMIN');
  };

  const isReadOnly = (): boolean => {
    return roles.length === 1 && hasRole('READONLY');
  };

  return {
    roles,
    permissions,
    isLoading,
    error,
    hasRole,
    hasAnyRole,
    canAccessSection,
    canModify,
    isReadOnly,
    refresh: loadMetaAdminRoles,
  };
}